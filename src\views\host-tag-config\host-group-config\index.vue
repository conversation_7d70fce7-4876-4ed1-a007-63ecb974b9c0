<template>
  <el-card class="host-group-config">
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchFormRef" label-position="left" inline>
      <el-row>
        <el-form-item label="IP地址" prop="ip">
          <el-input
            v-model="searchForm.ip"
            clearable
            placeholder="请输入IP地址"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item label="标签键" prop="tag_key">
          <el-input
            v-model="searchForm.tag_key"
            clearable
            placeholder="请输入标签键"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item label="标签值" prop="tag_value">
          <el-input
            v-model="searchForm.tag_value"
            clearable
            placeholder="请输入标签值"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <el-row style="margin-bottom: 15px;">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :disabled="selectedRows.length === 0"
      >
        批量删除
      </el-button>
    </el-row>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      v-loading="querying"
      border
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column prop="v4_ip_num" label="IPv4 VIP个数" align="center" width="120">
      </el-table-column>

      <el-table-column prop="v6_ip_num" label="IPv6 VIP个数" align="center" width="120">
      </el-table-column>

      <el-table-column prop="is_ipv6_enable" label="启用IPv6" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.is_ipv6_enable ? '是' : '否' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="lvs_mode" label="LVS模式" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.lvs_mode === 1 ? '模式1' : '模式2' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="lvs_group" label="LVS组" align="center" width="150">
      </el-table-column>

      <el-table-column prop="ips_code" label="IPS代码" align="center" width="120">
      </el-table-column>

      <el-table-column prop="resource_group" label="资源组" align="center" width="120">
      </el-table-column>

      <el-table-column prop="level" label="级别" align="center" width="80">
      </el-table-column>

      <el-table-column prop="ip_list" label="IP列表" align="center" min-width="200">
        <template slot-scope="scope">
          <span>{{ Array.isArray(scope.row.ip_list) ? scope.row.ip_list.join(', ') : scope.row.ip_list }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="remark" label="备注" align="center" min-width="150">
      </el-table-column>

      <el-table-column prop="operator" label="操作人" align="center" width="120">
      </el-table-column>

      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleEdit(scope.row)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 新增/修改弹窗 -->
    <host-group-plan-dialog
      :visible="dialogVisible"
      :is-edit="isEdit"
      :current-row-data="currentRowData"
      @close="dialogVisible = false"
      @refresh="onSearch"
    ></host-group-plan-dialog>

  </el-card>
</template>

<script>
import http from "@/views/host-tag-config/http.js";
import hostGroupPlanDialog from "./dialog/hostGroupPlanDialog.vue";

export default {
  name: "host-group-config",
  components: {
    hostGroupPlanDialog,
  },
  data() {
    return {
      querying: false,
      tableData: [],
      selectedRows: [],
      dialogVisible: false,
      isEdit: false,
      currentRowData: {},

      // 查询表单
      searchForm: {
        ip: "",
        tag_key: "",
        tag_value: "",
      },

      // 分页信息
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
    };
  },

  mounted() {
    this.onSearch();
  },

  created() {
  },

  methods: {
    // 查询数据
    async onSearch() {
      this.querying = true;
      try {
        const params = {
          ...this.searchForm,
          page: this.pagination.page,
          page_size: this.pagination.page_size,
        };

        const response = await http.getHostGroupPlanList(params);
        if (response && response.code === 100000) {
          // 处理数组格式的响应数据
          if (Array.isArray(response)) {
            this.tableData = response;
            this.pagination.total = response.length;
          } else if (response.data) {
            this.tableData = Array.isArray(response.data) ? response.data : [];
            this.pagination.total = response.total || response.data.length || 0;
          } else {
            this.tableData = [];
            this.pagination.total = 0;
          }
        } else {
          this.tableData = [];
          this.pagination.total = 0;
          if (response && response.message) {
            this.$message.warning(response.message);
          }
        }
      } catch (error) {
        console.error("查询主机组规划列表失败:", error);
        this.tableData = [];
        this.pagination.total = 0;
        this.$message.error("查询失败，请检查网络连接");
      } finally {
        this.querying = false;
      }
    },

    // 重置查询条件
    onReset() {
      this.searchForm = {
        ip: "",
        tag_key: "",
        tag_value: "",
      };
      this.pagination.page = 1;
      this.onSearch();
    },

    // 新增
    handleAdd() {
      this.isEdit = false;
      this.currentRowData = {};
      this.dialogVisible = true;
    },

    // 修改
    handleEdit(row) {
      this.isEdit = true;
      this.currentRowData = { ...row };
      this.dialogVisible = true;
    },

    // 删除单个记录
    async handleDelete(row) {
      this.$confirm(`确定要删除这条主机组规划记录吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const response = await http.deleteHostGroupPlan(row.id);
            if (response && response.code === 100000) {
              this.$message.success("删除成功");
              this.onSearch();
            } else {
              this.$message.error(response?.message || "删除失败");
            }
          } catch (error) {
            console.error("删除主机组规划失败:", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的记录");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedRows.length} 条记录吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          try {
            const deletePromises = this.selectedRows.map((row) =>
              http.deleteHostGroupPlan(row.id)
            );
            await Promise.all(deletePromises);
            this.$message.success("批量删除成功");
            this.onSearch();
          } catch (error) {
            console.error("批量删除主机组规划失败:", error);
            this.$message.error("批量删除失败");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.pagination.page = 1;
      this.onSearch();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.onSearch();
    },
  },
};
</script>

<style scoped>
.host-group-config {
  padding: 20px;
}
</style>
